"""
实验3：模拟输入（ADC）- 读取模拟信号
学习目标：ADC使用、电压测量、传感器读取
"""

import machine
import time

def basic_adc_read():
    """基础ADC读取"""
    print("=== 实验3：基础ADC读取 ===")
    print("连接说明：")
    print("- 电位器中间脚连接GP26（ADC0）")
    print("- 电位器两端分别连接3.3V和GND")
    print("- 或者用跳线连接不同电压到GP26测试")
    print()
    
    # 配置ADC
    adc = machine.ADC(26)  # GP26是ADC0
    
    print("ADC读取测试（10秒，每秒读取一次）...")
    
    for i in range(10):
        # 读取原始ADC值（0-65535）
        raw_value = adc.read_u16()
        
        # 转换为电压（0-3.3V）
        voltage = raw_value * 3.3 / 65535
        
        # 转换为百分比
        percentage = raw_value / 65535 * 100
        
        print(f"第{i+1}次读取:")
        print(f"  原始值: {raw_value}")
        print(f"  电压: {voltage:.3f}V")
        print(f"  百分比: {percentage:.1f}%")
        print()
        
        time.sleep(1)
    
    print("✓ 基础ADC读取完成")

def adc_led_control():
    """ADC控制LED亮度"""
    print("\n=== 实验3扩展：ADC控制LED亮度 ===")
    
    adc = machine.ADC(26)
    led_pwm = machine.PWM(machine.Pin(25))
    led_pwm.freq(1000)
    
    print("电位器控制LED亮度（20秒测试）...")
    
    start_time = time.time()
    while time.time() - start_time < 20:
        # 读取ADC值
        adc_value = adc.read_u16()
        
        # 直接用ADC值控制PWM占空比
        led_pwm.duty_u16(adc_value)
        
        # 显示当前状态
        brightness = adc_value / 65535 * 100
        print(f"亮度: {brightness:.1f}%", end='\r')
        
        time.sleep(0.1)
    
    led_pwm.deinit()
    print("\n✓ ADC控制LED亮度完成")

def temperature_sensor():
    """内置温度传感器"""
    print("\n=== 实验3高级：内置温度传感器 ===")
    
    # RP2040内置温度传感器
    temp_sensor = machine.ADC(4)  # ADC4是内置温度传感器
    
    print("内置温度传感器读取（10次）...")
    
    for i in range(10):
        # 读取温度传感器
        raw_temp = temp_sensor.read_u16()
        
        # 转换为电压
        voltage = raw_temp * 3.3 / 65535
        
        # 转换为温度（RP2040公式）
        temperature = 27 - (voltage - 0.706) / 0.001721
        
        print(f"第{i+1}次测量:")
        print(f"  原始值: {raw_temp}")
        print(f"  电压: {voltage:.3f}V")
        print(f"  温度: {temperature:.1f}°C")
        print()
        
        time.sleep(2)
    
    print("✓ 温度传感器读取完成")

def multi_channel_adc():
    """多通道ADC读取"""
    print("\n=== 实验3终极：多通道ADC ===")
    print("连接说明：")
    print("- 通道0（GP26）：电位器或可变电压")
    print("- 通道1（GP27）：光敏电阻或其他传感器")
    print("- 通道2（GP28）：另一个传感器")
    print()
    
    # 配置多个ADC通道
    adc_channels = {
        'CH0_GP26': machine.ADC(26),
        'CH1_GP27': machine.ADC(27),
        'CH2_GP28': machine.ADC(28),
        'TEMP': machine.ADC(4)  # 内置温度传感器
    }
    
    print("多通道ADC监控（15秒）...")
    
    start_time = time.time()
    sample_count = 0
    
    while time.time() - start_time < 15:
        sample_count += 1
        print(f"\n=== 采样 {sample_count} ===")
        
        for name, adc in adc_channels.items():
            raw_value = adc.read_u16()
            voltage = raw_value * 3.3 / 65535
            
            if name == 'TEMP':
                # 温度计算
                temperature = 27 - (voltage - 0.706) / 0.001721
                print(f"{name}: {temperature:.1f}°C ({voltage:.3f}V)")
            else:
                # 普通电压显示
                percentage = raw_value / 65535 * 100
                print(f"{name}: {voltage:.3f}V ({percentage:.1f}%)")
        
        time.sleep(2)
    
    print("\n✓ 多通道ADC监控完成")

def adc_data_logger():
    """ADC数据记录器"""
    print("\n=== 实验3专业：ADC数据记录 ===")
    
    adc = machine.ADC(26)
    
    # 数据存储
    data_log = []
    
    print("数据记录模式（每0.5秒记录一次，持续10秒）...")
    
    start_time = time.time()
    while time.time() - start_time < 10:
        timestamp = time.time() - start_time
        raw_value = adc.read_u16()
        voltage = raw_value * 3.3 / 65535
        
        # 记录数据
        data_point = {
            'time': timestamp,
            'raw': raw_value,
            'voltage': voltage
        }
        data_log.append(data_point)
        
        print(f"时间: {timestamp:.1f}s, 电压: {voltage:.3f}V")
        time.sleep(0.5)
    
    # 数据分析
    print("\n=== 数据分析 ===")
    voltages = [d['voltage'] for d in data_log]
    
    min_voltage = min(voltages)
    max_voltage = max(voltages)
    avg_voltage = sum(voltages) / len(voltages)
    
    print(f"采样次数: {len(data_log)}")
    print(f"最小电压: {min_voltage:.3f}V")
    print(f"最大电压: {max_voltage:.3f}V")
    print(f"平均电压: {avg_voltage:.3f}V")
    print(f"电压范围: {max_voltage - min_voltage:.3f}V")
    
    print("✓ ADC数据记录完成")

def main():
    """主函数"""
    print("YD-RP2040 模拟输入（ADC）实验开始...")
    print("硬件要求：")
    print("1. 电位器（推荐10kΩ）")
    print("2. 可选：光敏电阻、热敏电阻等传感器")
    print("3. 跳线若干")
    print()
    
    try:
        basic_adc_read()
        adc_led_control()
        temperature_sensor()
        multi_channel_adc()
        adc_data_logger()
    except Exception as e:
        print(f"实验出错：{e}")
    
    print("\n=== 实验3完成 ===")
    print("您已经掌握了ADC的使用和传感器数据处理！")

if __name__ == "__main__":
    main()
