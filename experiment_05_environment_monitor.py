"""
实验5：环境监控系统 - 综合应用项目
学习目标：系统集成、数据处理、用户交互
"""

import machine
import time
import json

class EnvironmentMonitor:
    """环境监控系统类"""
    
    def __init__(self):
        """初始化系统"""
        print("=== 环境监控系统初始化 ===")
        
        # 硬件初始化
        self.temp_sensor = machine.ADC(4)  # 内置温度传感器
        self.light_sensor = machine.ADC(26)  # 光线传感器（GP26）
        self.humidity_sensor = machine.ADC(27)  # 湿度传感器（GP27）
        
        # LED指示灯
        self.status_led = machine.Pin(25, machine.Pin.OUT)
        self.alarm_led = machine.PWM(machine.Pin(16))  # 报警LED（PWM）
        self.alarm_led.freq(1000)
        
        # 按钮
        self.mode_button = machine.Pin(15, machine.Pin.IN, machine.Pin.PULL_UP)
        self.reset_button = machine.Pin(14, machine.Pin.IN, machine.Pin.PULL_UP)
        
        # UART通信
        self.uart = machine.UART(1, baudrate=9600, tx=machine.Pin(4), rx=machine.Pin(5))
        
        # 系统状态
        self.monitoring = False
        self.alarm_active = False
        self.data_log = []
        self.thresholds = {
            'temp_min': 20.0,
            'temp_max': 30.0,
            'light_min': 0.5,
            'light_max': 2.8
        }
        
        print("✓ 系统初始化完成")
    
    def read_sensors(self):
        """读取所有传感器数据"""
        # 温度传感器
        temp_raw = self.temp_sensor.read_u16()
        temp_voltage = temp_raw * 3.3 / 65535
        temperature = 27 - (temp_voltage - 0.706) / 0.001721
        
        # 光线传感器
        light_raw = self.light_sensor.read_u16()
        light_voltage = light_raw * 3.3 / 65535
        
        # 湿度传感器（模拟）
        humidity_raw = self.humidity_sensor.read_u16()
        humidity_voltage = humidity_raw * 3.3 / 65535
        humidity_percent = humidity_voltage / 3.3 * 100
        
        return {
            'timestamp': time.time(),
            'temperature': round(temperature, 2),
            'light': round(light_voltage, 3),
            'humidity': round(humidity_percent, 1),
            'temp_raw': temp_raw,
            'light_raw': light_raw,
            'humidity_raw': humidity_raw
        }
    
    def check_alarms(self, data):
        """检查报警条件"""
        alarms = []
        
        # 温度报警
        if data['temperature'] < self.thresholds['temp_min']:
            alarms.append(f"温度过低: {data['temperature']}°C")
        elif data['temperature'] > self.thresholds['temp_max']:
            alarms.append(f"温度过高: {data['temperature']}°C")
        
        # 光线报警
        if data['light'] < self.thresholds['light_min']:
            alarms.append(f"光线过暗: {data['light']}V")
        elif data['light'] > self.thresholds['light_max']:
            alarms.append(f"光线过亮: {data['light']}V")
        
        return alarms
    
    def handle_alarms(self, alarms):
        """处理报警"""
        if alarms:
            if not self.alarm_active:
                print("⚠️  报警激活!")
                self.alarm_active = True
            
            # 闪烁报警LED
            for _ in range(3):
                self.alarm_led.duty_u16(32768)  # 50%亮度
                time.sleep(0.2)
                self.alarm_led.duty_u16(0)
                time.sleep(0.2)
            
            # 发送报警信息
            for alarm in alarms:
                print(f"🚨 {alarm}")
                self.uart.write(f"ALARM: {alarm}\n")
        else:
            if self.alarm_active:
                print("✅ 报警解除")
                self.alarm_active = False
                self.alarm_led.duty_u16(0)
    
    def display_data(self, data):
        """显示数据"""
        print(f"\n=== 环境数据 ===")
        print(f"时间: {time.time():.0f}")
        print(f"温度: {data['temperature']}°C")
        print(f"光线: {data['light']}V")
        print(f"湿度: {data['humidity']}%")
        
        # 状态指示
        status = "正常" if not self.alarm_active else "报警"
        print(f"状态: {status}")
    
    def send_data_packet(self, data):
        """发送数据包"""
        # JSON格式数据包
        packet = {
            'type': 'sensor_data',
            'timestamp': data['timestamp'],
            'sensors': {
                'temperature': data['temperature'],
                'light': data['light'],
                'humidity': data['humidity']
            },
            'status': 'alarm' if self.alarm_active else 'normal'
        }
        
        json_str = json.dumps(packet)
        self.uart.write(f"DATA: {json_str}\n")
    
    def check_buttons(self):
        """检查按钮状态"""
        # 模式切换按钮
        if not self.mode_button.value():
            self.monitoring = not self.monitoring
            status = "开始" if self.monitoring else "停止"
            print(f"📊 监控{status}")
            time.sleep(0.5)  # 防抖动
        
        # 重置按钮
        if not self.reset_button.value():
            self.data_log.clear()
            self.alarm_active = False
            self.alarm_led.duty_u16(0)
            print("🔄 系统重置")
            time.sleep(0.5)
    
    def run_monitoring(self):
        """运行监控循环"""
        print("\n=== 开始环境监控 ===")
        print("按钮控制：")
        print("- GP15按钮：开始/停止监控")
        print("- GP14按钮：重置系统")
        print("- Ctrl+C：退出程序")
        print()
        
        self.uart.write("环境监控系统启动\n")
        
        try:
            while True:
                # 检查按钮
                self.check_buttons()
                
                # 状态LED指示
                if self.monitoring:
                    self.status_led.on()
                    
                    # 读取传感器
                    sensor_data = self.read_sensors()
                    
                    # 记录数据
                    self.data_log.append(sensor_data)
                    
                    # 显示数据
                    self.display_data(sensor_data)
                    
                    # 检查报警
                    alarms = self.check_alarms(sensor_data)
                    self.handle_alarms(alarms)
                    
                    # 发送数据
                    self.send_data_packet(sensor_data)
                    
                    # 限制数据日志大小
                    if len(self.data_log) > 100:
                        self.data_log = self.data_log[-50:]
                    
                    time.sleep(3)  # 3秒采样间隔
                else:
                    self.status_led.off()
                    time.sleep(0.5)
        
        except KeyboardInterrupt:
            print("\n🛑 监控停止")
            self.cleanup()
    
    def show_statistics(self):
        """显示统计信息"""
        if not self.data_log:
            print("📊 暂无数据")
            return
        
        print(f"\n=== 统计信息 ===")
        print(f"数据点数量: {len(self.data_log)}")
        
        # 温度统计
        temps = [d['temperature'] for d in self.data_log]
        print(f"温度 - 最小: {min(temps):.1f}°C, 最大: {max(temps):.1f}°C, 平均: {sum(temps)/len(temps):.1f}°C")
        
        # 光线统计
        lights = [d['light'] for d in self.data_log]
        print(f"光线 - 最小: {min(lights):.2f}V, 最大: {max(lights):.2f}V, 平均: {sum(lights)/len(lights):.2f}V")
        
        # 湿度统计
        humidities = [d['humidity'] for d in self.data_log]
        print(f"湿度 - 最小: {min(humidities):.1f}%, 最大: {max(humidities):.1f}%, 平均: {sum(humidities)/len(humidities):.1f}%")
    
    def cleanup(self):
        """清理资源"""
        self.status_led.off()
        self.alarm_led.duty_u16(0)
        self.alarm_led.deinit()
        print("✓ 系统清理完成")

def main():
    """主函数"""
    print("YD-RP2040 环境监控系统")
    print("=" * 40)
    print("硬件连接要求：")
    print("1. 光线传感器（电位器）连接GP26")
    print("2. 湿度传感器（电位器）连接GP27")
    print("3. 状态LED连接GP25")
    print("4. 报警LED连接GP16")
    print("5. 模式按钮连接GP15")
    print("6. 重置按钮连接GP14")
    print("7. UART: TX=GP4, RX=GP5")
    print()
    
    # 创建监控系统
    monitor = EnvironmentMonitor()
    
    try:
        # 运行监控
        monitor.run_monitoring()
    except Exception as e:
        print(f"系统错误: {e}")
    finally:
        # 显示统计信息
        monitor.show_statistics()
        monitor.cleanup()
    
    print("\n=== 实验5完成 ===")
    print("恭喜！您已经完成了一个完整的嵌入式系统项目！")

if __name__ == "__main__":
    main()
