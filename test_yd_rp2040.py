"""
YD-RP2040开发板测试脚本
用于验证硬件连接和基本功能
"""

import machine
import time
import network

def test_basic_functions():
    """测试基本功能"""
    print("=== YD-RP2040基本功能测试 ===")
    
    # 测试板载LED（如果有）
    try:
        led = machine.Pin(25, machine.Pin.OUT)  # 通常是GP25
        print("测试板载LED...")
        for i in range(3):
            led.on()
            time.sleep(0.5)
            led.off()
            time.sleep(0.5)
        print("✓ LED测试完成")
    except:
        print("! 未找到板载LED或引脚不同")
    
    # 测试433MHz引脚
    print("\n测试433MHz引脚...")
    tx_pin = machine.Pin(27, machine.Pin.OUT)
    rx_pin = machine.Pin(22, machine.Pin.IN, machine.Pin.PULL_DOWN)
    
    # 简单的引脚测试
    tx_pin.on()
    time.sleep(0.1)
    tx_pin.off()
    print("✓ TX引脚(GP27)测试完成")
    print("✓ RX引脚(GP22)配置完成")

def test_wifi():
    """测试WiFi功能"""
    print("\n=== WiFi功能测试 ===")
    
    wlan = network.WLAN(network.STA_IF)
    wlan.active(True)
    
    print("扫描WiFi网络...")
    networks = wlan.scan()
    print(f"发现 {len(networks)} 个WiFi网络:")
    for net in networks[:5]:  # 显示前5个
        ssid = net[0].decode('utf-8')
        signal = net[3]
        print(f"  - {ssid} (信号强度: {signal})")
    
    print("✓ WiFi功能正常")

def test_memory():
    """测试内存信息"""
    print("\n=== 内存信息 ===")
    import gc
    gc.collect()
    free_mem = gc.mem_free()
    alloc_mem = gc.mem_alloc()
    total_mem = free_mem + alloc_mem
    
    print(f"总内存: {total_mem} bytes")
    print(f"已用内存: {alloc_mem} bytes")
    print(f"可用内存: {free_mem} bytes")
    print(f"内存使用率: {(alloc_mem/total_mem)*100:.1f}%")

def main():
    """主测试函数"""
    print("YD-RP2040开发板测试开始...")
    print(f"MicroPython版本: {machine.freq()//1000000}MHz")
    
    test_basic_functions()
    test_wifi()
    test_memory()
    
    print("\n=== 测试完成 ===")
    print("如果所有测试都通过，您的YD-RP2040已准备好运行433MHz项目！")

if __name__ == "__main__":
    main()
