"""
OLED基础测试 - YD-RP2040
测试SSD1306 OLED屏幕的基本功能
"""

import machine
import time
from ssd1306 import SSD1306_I2C

def test_oled_basic():
    """基础OLED测试"""
    print("=== OLED基础测试开始 ===")
    
    # 配置I2C
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    
    # 扫描I2C设备
    print("扫描I2C设备...")
    devices = i2c.scan()
    if devices:
        print(f"发现I2C设备: {[hex(device) for device in devices]}")
    else:
        print("未发现I2C设备，请检查连接！")
        return False
    
    # 初始化OLED (128x64)
    try:
        oled = SSD1306_I2C(128, 64, i2c)
        print("✓ OLED初始化成功")
    except Exception as e:
        print(f"✗ OLED初始化失败: {e}")
        return False
    
    # 清屏测试
    print("清屏测试...")
    oled.fill(0)
    oled.show()
    time.sleep(1)
    
    # 显示文本
    print("显示文本测试...")
    oled.text('Hello YD-RP2040!', 0, 0)
    oled.text('OLED Test', 0, 10)
    oled.text('128x64 Display', 0, 20)
    oled.show()
    time.sleep(2)
    
    # 显示像素点
    print("像素点测试...")
    oled.fill(0)
    for x in range(0, 128, 4):
        for y in range(0, 64, 4):
            oled.pixel(x, y, 1)
    oled.show()
    time.sleep(2)
    
    # 显示线条
    print("线条测试...")
    oled.fill(0)
    # 水平线
    oled.hline(0, 10, 128, 1)
    oled.hline(0, 30, 128, 1)
    oled.hline(0, 50, 128, 1)
    # 垂直线
    oled.vline(20, 0, 64, 1)
    oled.vline(60, 0, 64, 1)
    oled.vline(100, 0, 64, 1)
    oled.show()
    time.sleep(2)
    
    # 显示矩形
    print("矩形测试...")
    oled.fill(0)
    oled.rect(10, 10, 30, 20, 1)  # 空心矩形
    oled.fill_rect(50, 10, 30, 20, 1)  # 实心矩形
    oled.rect(90, 10, 30, 20, 1)
    oled.show()
    time.sleep(2)
    
    print("✓ OLED基础测试完成")
    return True

def test_oled_animation():
    """OLED动画测试"""
    print("\n=== OLED动画测试 ===")
    
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    oled = SSD1306_I2C(128, 64, i2c)
    
    # 滚动文本
    print("滚动文本...")
    text = "YD-RP2040 OLED Display Test - Rolling Text Demo"
    for offset in range(len(text) * 8):
        oled.fill(0)
        oled.text(text, -offset, 20)
        oled.show()
        time.sleep(0.1)
    
    # 弹跳球动画
    print("弹跳球动画...")
    ball_x, ball_y = 64, 32
    dx, dy = 2, 1
    
    for _ in range(100):
        oled.fill(0)
        
        # 更新球的位置
        ball_x += dx
        ball_y += dy
        
        # 边界检测
        if ball_x <= 2 or ball_x >= 125:
            dx = -dx
        if ball_y <= 2 or ball_y >= 61:
            dy = -dy
        
        # 绘制球（3x3像素）
        oled.fill_rect(ball_x-1, ball_y-1, 3, 3, 1)
        
        # 绘制边框
        oled.rect(0, 0, 128, 64, 1)
        
        oled.show()
        time.sleep(0.05)
    
    print("✓ 动画测试完成")

def test_oled_graphics():
    """OLED图形测试"""
    print("\n=== OLED图形测试 ===")
    
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    oled = SSD1306_I2C(128, 64, i2c)
    
    # 绘制圆形（近似）
    print("圆形测试...")
    oled.fill(0)
    center_x, center_y = 64, 32
    radius = 20
    
    for angle in range(0, 360, 5):
        import math
        x = int(center_x + radius * math.cos(math.radians(angle)))
        y = int(center_y + radius * math.sin(math.radians(angle)))
        if 0 <= x < 128 and 0 <= y < 64:
            oled.pixel(x, y, 1)
    
    oled.show()
    time.sleep(2)
    
    # 绘制波形
    print("波形测试...")
    oled.fill(0)
    
    for x in range(128):
        import math
        y = int(32 + 20 * math.sin(x * 0.1))
        if 0 <= y < 64:
            oled.pixel(x, y, 1)
    
    oled.show()
    time.sleep(2)
    
    # 绘制进度条
    print("进度条测试...")
    for progress in range(0, 101, 5):
        oled.fill(0)
        oled.text(f'Progress: {progress}%', 0, 0)
        
        # 进度条外框
        oled.rect(10, 20, 108, 12, 1)
        
        # 进度条填充
        fill_width = int(106 * progress / 100)
        if fill_width > 0:
            oled.fill_rect(11, 21, fill_width, 10, 1)
        
        oled.show()
        time.sleep(0.1)
    
    print("✓ 图形测试完成")

def test_oled_info_display():
    """OLED信息显示测试"""
    print("\n=== OLED信息显示测试 ===")
    
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    oled = SSD1306_I2C(128, 64, i2c)
    
    # 显示系统信息
    import gc
    
    for i in range(10):
        oled.fill(0)
        
        # 标题
        oled.text('YD-RP2040 Info', 0, 0)
        oled.hline(0, 8, 128, 1)
        
        # 内存信息
        gc.collect()
        free_mem = gc.mem_free()
        used_mem = gc.mem_alloc()
        
        oled.text(f'Free: {free_mem}B', 0, 12)
        oled.text(f'Used: {used_mem}B', 0, 22)
        
        # 运行时间
        oled.text(f'Time: {time.time():.1f}s', 0, 32)
        
        # 计数器
        oled.text(f'Count: {i+1}', 0, 42)
        
        # 状态指示
        status = "Running..." if i % 2 == 0 else "Active..."
        oled.text(status, 0, 52)
        
        oled.show()
        time.sleep(1)
    
    print("✓ 信息显示测试完成")

def main():
    """主测试函数"""
    print("YD-RP2040 OLED驱动测试")
    print("=" * 40)
    print("硬件连接:")
    print("OLED VCC → YD-RP2040 3.3V")
    print("OLED GND → YD-RP2040 GND")
    print("OLED SCL → YD-RP2040 GP21")
    print("OLED SDA → YD-RP2040 GP20")
    print()
    
    try:
        # 基础测试
        if not test_oled_basic():
            print("基础测试失败，请检查硬件连接")
            return
        
        # 动画测试
        test_oled_animation()
        
        # 图形测试
        test_oled_graphics()
        
        # 信息显示测试
        test_oled_info_display()
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
    
    print("\n=== 所有测试完成 ===")
    print("如果看到显示内容，说明OLED驱动成功！")

if __name__ == "__main__":
    main()
