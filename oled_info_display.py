"""
OLED信息显示 - 实时系统状态
显示YD-RP2040的运行状态信息
"""

import machine
import time
import gc

def create_info_display():
    """创建信息显示系统"""
    # 初始化OLED
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    from ssd1306_simple import SSD1306_I2C
    oled = SSD1306_I2C(128, 64, i2c)
    
    # 初始化LED（状态指示）
    led = machine.Pin(25, machine.Pin.OUT)
    
    print("信息显示系统启动...")
    print("按Ctrl+C停止")
    
    counter = 0
    
    try:
        while True:
            # 清屏
            oled.fill(0)
            
            # 标题
            oled.text('YD-RP2040 Status', 0, 0)
            oled.hline(0, 8, 128, 1)  # 分割线
            
            # 运行时间
            runtime = time.time()
            oled.text(f'Time: {runtime:.1f}s', 0, 12)
            
            # 内存信息
            gc.collect()
            free_mem = gc.mem_free()
            used_mem = gc.mem_alloc()
            total_mem = free_mem + used_mem
            mem_percent = (used_mem / total_mem) * 100
            
            oled.text(f'Mem: {mem_percent:.1f}%', 0, 22)
            oled.text(f'Free: {free_mem}B', 0, 32)
            
            # 计数器
            oled.text(f'Count: {counter}', 0, 42)
            
            # 状态指示
            status = "Active" if counter % 2 == 0 else "Running"
            oled.text(f'Status: {status}', 0, 52)
            
            # 刷新显示
            oled.show()
            
            # LED闪烁
            led.toggle()
            
            counter += 1
            time.sleep(1)
            
    except KeyboardInterrupt:
        oled.fill(0)
        oled.text('System Stopped', 0, 20)
        oled.show()
        led.off()
        print("\n信息显示系统已停止")

if __name__ == "__main__":
    create_info_display()
