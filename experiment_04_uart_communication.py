"""
实验4：串口通信（UART）- 数据传输学习
学习目标：UART配置、数据发送接收、通信协议
"""

import machine
import time

def basic_uart_test():
    """基础UART测试"""
    print("=== 实验4：基础UART测试 ===")
    print("连接说明：")
    print("- UART1 TX (GP4) 连接到 UART1 RX (GP5) 进行回环测试")
    print("- 或连接到其他设备进行通信测试")
    print()
    
    # 配置UART1
    uart = machine.UART(1, baudrate=9600, tx=machine.Pin(4), rx=machine.Pin(5))
    
    print("UART配置信息：")
    print(f"波特率: 9600")
    print(f"TX引脚: GP4")
    print(f"RX引脚: GP5")
    print()
    
    # 发送测试数据
    test_messages = [
        "Hello YD-RP2040!",
        "UART Test Message",
        "123456789",
        "Special chars: !@#$%^&*()"
    ]
    
    print("发送测试消息...")
    for i, msg in enumerate(test_messages):
        print(f"发送: {msg}")
        uart.write(msg + '\n')
        time.sleep(0.5)
        
        # 尝试读取回应（如果有回环连接）
        if uart.any():
            received = uart.read().decode('utf-8').strip()
            print(f"接收: {received}")
        else:
            print("无回应数据")
        print()
    
    print("✓ 基础UART测试完成")

def uart_command_interface():
    """UART命令接口"""
    print("\n=== 实验4扩展：UART命令接口 ===")
    
    uart = machine.UART(1, baudrate=9600, tx=machine.Pin(4), rx=machine.Pin(5))
    led = machine.Pin(25, machine.Pin.OUT)
    
    # 命令处理函数
    def process_command(cmd):
        cmd = cmd.strip().upper()
        
        if cmd == "LED_ON":
            led.on()
            return "LED已开启"
        elif cmd == "LED_OFF":
            led.off()
            return "LED已关闭"
        elif cmd == "LED_TOGGLE":
            led.toggle()
            return f"LED状态切换，当前: {'开启' if led.value() else '关闭'}"
        elif cmd == "STATUS":
            return f"LED状态: {'开启' if led.value() else '关闭'}"
        elif cmd == "HELP":
            return "命令: LED_ON, LED_OFF, LED_TOGGLE, STATUS, HELP, QUIT"
        elif cmd == "QUIT":
            return "QUIT"
        else:
            return f"未知命令: {cmd}"
    
    print("UART命令接口启动...")
    print("发送命令: LED_ON, LED_OFF, LED_TOGGLE, STATUS, HELP, QUIT")
    
    uart.write("YD-RP2040 命令接口就绪\n")
    uart.write("输入 HELP 查看可用命令\n")
    
    command_buffer = ""
    
    try:
        while True:
            if uart.any():
                # 读取数据
                data = uart.read().decode('utf-8')
                command_buffer += data
                
                # 检查是否有完整命令（以换行符结束）
                if '\n' in command_buffer:
                    lines = command_buffer.split('\n')
                    for line in lines[:-1]:  # 处理完整的行
                        if line.strip():
                            response = process_command(line)
                            print(f"收到命令: {line.strip()}")
                            print(f"响应: {response}")
                            uart.write(f"响应: {response}\n")
                            
                            if response == "QUIT":
                                return
                    
                    command_buffer = lines[-1]  # 保留未完成的行
            
            time.sleep(0.1)
    
    except KeyboardInterrupt:
        uart.write("命令接口关闭\n")
        print("\n✓ UART命令接口测试完成")

def uart_sensor_data():
    """UART传感器数据传输"""
    print("\n=== 实验4高级：传感器数据传输 ===")
    
    uart = machine.UART(1, baudrate=9600, tx=machine.Pin(4), rx=machine.Pin(5))
    
    # 模拟传感器（使用内置温度传感器和ADC）
    temp_sensor = machine.ADC(4)
    adc_sensor = machine.ADC(26)
    
    print("传感器数据传输（10次采样）...")
    
    for i in range(10):
        # 读取传感器数据
        temp_raw = temp_sensor.read_u16()
        temp_voltage = temp_raw * 3.3 / 65535
        temperature = 27 - (temp_voltage - 0.706) / 0.001721
        
        adc_raw = adc_sensor.read_u16()
        adc_voltage = adc_raw * 3.3 / 65535
        
        # 创建数据包
        timestamp = time.time()
        data_packet = f"DATA,{i+1},{timestamp:.2f},{temperature:.2f},{adc_voltage:.3f}\n"
        
        print(f"发送数据包 {i+1}: {data_packet.strip()}")
        uart.write(data_packet)
        
        time.sleep(2)
    
    print("✓ 传感器数据传输完成")

def uart_protocol_demo():
    """UART协议演示"""
    print("\n=== 实验4专业：自定义协议演示 ===")
    
    uart = machine.UART(1, baudrate=9600, tx=machine.Pin(4), rx=machine.Pin(5))
    
    # 简单的数据包协议
    # 格式: START|TYPE|LENGTH|DATA|CHECKSUM|END
    
    def create_packet(packet_type, data):
        """创建数据包"""
        data_str = str(data)
        length = len(data_str)
        
        # 简单校验和
        checksum = sum(ord(c) for c in data_str) % 256
        
        packet = f"START|{packet_type}|{length:02d}|{data_str}|{checksum:02X}|END\n"
        return packet
    
    def parse_packet(packet):
        """解析数据包"""
        try:
            parts = packet.strip().split('|')
            if len(parts) != 6 or parts[0] != 'START' or parts[5] != 'END':
                return None, "格式错误"
            
            packet_type = parts[1]
            length = int(parts[2])
            data = parts[3]
            checksum = int(parts[4], 16)
            
            # 验证长度
            if len(data) != length:
                return None, "长度错误"
            
            # 验证校验和
            calc_checksum = sum(ord(c) for c in data) % 256
            if calc_checksum != checksum:
                return None, "校验和错误"
            
            return {'type': packet_type, 'data': data}, "成功"
        
        except Exception as e:
            return None, f"解析错误: {e}"
    
    # 发送不同类型的数据包
    test_packets = [
        ("TEMP", "25.6"),
        ("VOLT", "3.14"),
        ("MSG", "Hello"),
        ("CMD", "LED_ON"),
        ("ERR", "Test Error")
    ]
    
    print("协议演示 - 发送数据包...")
    
    for packet_type, data in test_packets:
        packet = create_packet(packet_type, data)
        print(f"发送: {packet.strip()}")
        uart.write(packet)
        
        # 模拟解析（自己解析自己发送的包）
        parsed, status = parse_packet(packet)
        if parsed:
            print(f"解析成功: 类型={parsed['type']}, 数据={parsed['data']}")
        else:
            print(f"解析失败: {status}")
        print()
        
        time.sleep(1)
    
    print("✓ 协议演示完成")

def main():
    """主函数"""
    print("YD-RP2040 UART通信实验开始...")
    print("硬件连接：")
    print("1. 回环测试：GP4(TX) 连接到 GP5(RX)")
    print("2. 外部设备：连接到其他UART设备")
    print("3. USB转TTL模块：连接到电脑进行调试")
    print()
    
    try:
        basic_uart_test()
        uart_command_interface()
        uart_sensor_data()
        uart_protocol_demo()
    except Exception as e:
        print(f"实验出错：{e}")
    
    print("\n=== 实验4完成 ===")
    print("您已经掌握了UART通信和协议设计！")

if __name__ == "__main__":
    main()
