"""
实验1：LED闪烁 - YD-RP2040入门
学习目标：GPIO输出控制、基本延时
"""

import machine
import time

def basic_led_blink():
    """基础LED闪烁"""
    print("=== 实验1：基础LED闪烁 ===")
    
    # 尝试不同的LED引脚（YD-RP2040可能有板载LED）
    led_pins = [25, 2, 16]  # 常见的LED引脚
    led = None
    
    for pin_num in led_pins:
        try:
            led = machine.Pin(pin_num, machine.Pin.OUT)
            print(f"使用引脚 GP{pin_num} 作为LED")
            break
        except:
            continue
    
    if led is None:
        print("未找到合适的LED引脚，使用外接LED连接到GP25")
        led = machine.Pin(25, machine.Pin.OUT)
    
    # 基础闪烁
    print("开始LED闪烁（10次）...")
    for i in range(10):
        led.on()
        print(f"LED开启 - 第{i+1}次")
        time.sleep(0.5)
        led.off()
        print(f"LED关闭 - 第{i+1}次")
        time.sleep(0.5)
    
    print("✓ 基础LED闪烁完成")

def pattern_led_blink():
    """模式LED闪烁"""
    print("\n=== 实验1扩展：模式LED闪烁 ===")
    
    led = machine.Pin(25, machine.Pin.OUT)
    
    # SOS模式（摩尔斯电码）
    print("SOS信号模式...")
    def short_blink():
        led.on()
        time.sleep(0.2)
        led.off()
        time.sleep(0.2)
    
    def long_blink():
        led.on()
        time.sleep(0.6)
        led.off()
        time.sleep(0.2)
    
    # S-O-S
    for _ in range(3):
        short_blink()  # S
    time.sleep(0.4)
    
    for _ in range(3):
        long_blink()   # O
    time.sleep(0.4)
    
    for _ in range(3):
        short_blink()  # S
    
    print("✓ SOS信号完成")

def breathing_led():
    """呼吸灯效果（PWM）"""
    print("\n=== 实验1高级：呼吸灯效果 ===")
    
    # 使用PWM实现呼吸灯
    pwm_led = machine.PWM(machine.Pin(25))
    pwm_led.freq(1000)  # 1kHz频率
    
    print("呼吸灯效果（5个周期）...")
    for cycle in range(5):
        # 渐亮
        for duty in range(0, 65536, 1000):
            pwm_led.duty_u16(duty)
            time.sleep(0.01)
        
        # 渐暗
        for duty in range(65535, 0, -1000):
            pwm_led.duty_u16(duty)
            time.sleep(0.01)
        
        print(f"呼吸周期 {cycle+1}/5 完成")
    
    pwm_led.deinit()
    print("✓ 呼吸灯效果完成")

def main():
    """主函数"""
    print("YD-RP2040 LED控制实验开始...")
    print("请确保：")
    print("1. 如果没有板载LED，请将LED连接到GP25和GND")
    print("2. LED长脚连接GP25，短脚连接GND（可加220Ω电阻）")
    print()
    
    basic_led_blink()
    pattern_led_blink()
    breathing_led()
    
    print("\n=== 实验1完成 ===")
    print("恭喜！您已经掌握了基础的GPIO输出控制")

if __name__ == "__main__":
    main()
