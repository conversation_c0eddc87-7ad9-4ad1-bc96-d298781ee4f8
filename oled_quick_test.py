"""
OLED快速测试 - YD-RP2040
测试I2C连接的SSD1306 OLED屏幕
"""

import machine
import time

def quick_test():
    """快速测试OLED连接"""
    print("=== OLED快速测试 ===")
    
    # 1. 配置I2C
    print("1. 配置I2C接口...")
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    
    # 2. 扫描I2C设备
    print("2. 扫描I2C设备...")
    devices = i2c.scan()
    print(f"发现的设备地址: {[hex(addr) for addr in devices]}")
    
    if not devices:
        print("❌ 未发现I2C设备！")
        print("请检查:")
        print("- OLED VCC → 3.3V")
        print("- OLED GND → GND")
        print("- OLED SCL → GP21")
        print("- OLED SDA → GP20")
        return False
    
    # 3. 常见OLED地址检查
    common_addrs = [0x3C, 0x3D]
    oled_addr = None
    
    for addr in devices:
        if addr in common_addrs:
            oled_addr = addr
            break
    
    if oled_addr:
        print(f"✅ 发现OLED设备，地址: {hex(oled_addr)}")
    else:
        print(f"⚠️  发现设备但地址不常见: {[hex(addr) for addr in devices]}")
        oled_addr = devices[0]  # 使用第一个设备
    
    # 4. 初始化OLED
    print("3. 初始化OLED...")
    try:
        from ssd1306_simple import SSD1306_I2C
        oled = SSD1306_I2C(128, 64, i2c, oled_addr)
        print("✅ OLED初始化成功！")
    except Exception as e:
        print(f"❌ OLED初始化失败: {e}")
        return False
    
    # 5. 显示测试
    print("4. 显示测试...")
    
    # 清屏
    oled.fill(0)
    oled.show()
    time.sleep(0.5)
    
    # 显示文本
    oled.text('Hello!', 0, 0)
    oled.text('YD-RP2040', 0, 10)
    oled.text('OLED Works!', 0, 20)
    oled.show()
    
    print("✅ 如果屏幕显示文字，说明驱动成功！")
    return True

def demo_functions():
    """演示OLED各种功能"""
    print("\n=== OLED功能演示 ===")
    
    # 初始化
    i2c = machine.I2C(1, scl=machine.Pin(21), sda=machine.Pin(20), freq=400000)
    from ssd1306_simple import SSD1306_I2C
    oled = SSD1306_I2C(128, 64, i2c)
    
    # 1. 文本显示
    print("1. 文本显示演示...")
    oled.fill(0)
    oled.text('Text Demo', 0, 0)
    oled.text('Line 1', 0, 10)
    oled.text('Line 2', 0, 20)
    oled.text('Line 3', 0, 30)
    oled.text('Line 4', 0, 40)
    oled.text('Line 5', 0, 50)
    oled.show()
    time.sleep(2)
    
    # 2. 像素点
    print("2. 像素点演示...")
    oled.fill(0)
    for x in range(0, 128, 8):
        for y in range(0, 64, 8):
            oled.pixel(x, y, 1)
    oled.show()
    time.sleep(2)
    
    # 3. 线条
    print("3. 线条演示...")
    oled.fill(0)
    # 水平线
    for y in range(0, 64, 10):
        oled.hline(0, y, 128, 1)
    # 垂直线
    for x in range(0, 128, 20):
        oled.vline(x, 0, 64, 1)
    oled.show()
    time.sleep(2)
    
    # 4. 矩形
    print("4. 矩形演示...")
    oled.fill(0)
    oled.rect(10, 10, 40, 20, 1)  # 空心矩形
    oled.fill_rect(60, 10, 40, 20, 1)  # 实心矩形
    oled.rect(10, 40, 40, 15, 1)
    oled.fill_rect(60, 40, 40, 15, 1)
    oled.show()
    time.sleep(2)
    
    # 5. 动态计数
    print("5. 动态计数演示...")
    for i in range(10):
        oled.fill(0)
        oled.text('Counter Demo', 0, 0)
        oled.text(f'Count: {i}', 0, 20)
        oled.text(f'Time: {time.time():.1f}', 0, 40)
        oled.show()
        time.sleep(0.5)
    
    print("✅ 功能演示完成！")

def main():
    """主函数"""
    print("YD-RP2040 OLED I2C驱动测试")
    print("=" * 35)
    
    # 快速测试
    if quick_test():
        # 功能演示
        demo_functions()
    else:
        print("\n请检查硬件连接后重试")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
