"""
实验2：按钮输入控制 - GPIO输入学习
学习目标：GPIO输入、上拉电阻、中断处理
"""

import machine
import time

def basic_button_read():
    """基础按钮读取"""
    print("=== 实验2：基础按钮读取 ===")
    print("连接说明：")
    print("- 按钮一端连接GP15")
    print("- 按钮另一端连接GND")
    print("- 使用内部上拉电阻")
    print()
    
    # 配置按钮引脚（使用内部上拉电阻）
    button = machine.Pin(15, machine.Pin.IN, machine.Pin.PULL_UP)
    led = machine.Pin(25, machine.Pin.OUT)
    
    print("按钮测试开始（按按钮控制LED，按Ctrl+C退出）...")
    
    try:
        while True:
            if not button.value():  # 按钮按下（低电平）
                led.on()
                print("按钮按下 - LED开启")
                time.sleep(0.1)  # 防抖动
            else:
                led.off()
                print("按钮释放 - LED关闭")
                time.sleep(0.1)
    except KeyboardInterrupt:
        led.off()
        print("\n✓ 基础按钮测试完成")

def button_counter():
    """按钮计数器"""
    print("\n=== 实验2扩展：按钮计数器 ===")
    
    button = machine.Pin(15, machine.Pin.IN, machine.Pin.PULL_UP)
    led = machine.Pin(25, machine.Pin.OUT)
    
    count = 0
    last_state = True  # 上次按钮状态
    
    print("按钮计数器（每按一次计数+1，按10次结束）...")
    
    while count < 10:
        current_state = button.value()
        
        # 检测按钮从释放到按下的变化（下降沿）
        if last_state and not current_state:
            count += 1
            print(f"按钮按下次数: {count}")
            
            # LED闪烁指示
            for _ in range(count % 5 + 1):  # 闪烁次数表示个位数
                led.on()
                time.sleep(0.1)
                led.off()
                time.sleep(0.1)
            
            time.sleep(0.3)  # 防抖动
        
        last_state = current_state
        time.sleep(0.01)
    
    print("✓ 按钮计数器完成")

def button_interrupt():
    """按钮中断处理"""
    print("\n=== 实验2高级：按钮中断 ===")
    
    button = machine.Pin(15, machine.Pin.IN, machine.Pin.PULL_UP)
    led = machine.Pin(25, machine.Pin.OUT)
    
    # 全局变量
    button_pressed = False
    press_count = 0
    
    def button_handler(pin):
        """按钮中断处理函数"""
        nonlocal button_pressed, press_count
        button_pressed = True
        press_count += 1
        print(f"中断触发！按钮按下次数: {press_count}")
    
    # 设置中断（下降沿触发）
    button.irq(trigger=machine.Pin.IRQ_FALLING, handler=button_handler)
    
    print("中断模式按钮测试（按5次按钮结束）...")
    
    try:
        while press_count < 5:
            if button_pressed:
                # 处理按钮按下事件
                led.on()
                time.sleep(0.2)
                led.off()
                button_pressed = False
            
            time.sleep(0.1)
    except KeyboardInterrupt:
        pass
    
    # 清除中断
    button.irq(None)
    led.off()
    print("✓ 按钮中断测试完成")

def multi_button_control():
    """多按钮控制"""
    print("\n=== 实验2终极：多按钮控制 ===")
    print("连接说明：")
    print("- 按钮1连接GP14和GND（控制LED1）")
    print("- 按钮2连接GP15和GND（控制LED2）")
    print("- LED1连接GP25")
    print("- LED2连接GP26（如果有的话）")
    print()
    
    button1 = machine.Pin(14, machine.Pin.IN, machine.Pin.PULL_UP)
    button2 = machine.Pin(15, machine.Pin.IN, machine.Pin.PULL_UP)
    led1 = machine.Pin(25, machine.Pin.OUT)
    
    # 尝试使用第二个LED
    try:
        led2 = machine.Pin(26, machine.Pin.OUT)
        has_led2 = True
    except:
        has_led2 = False
        print("注意：只使用一个LED")
    
    print("多按钮控制测试（按两个按钮同时按下退出）...")
    
    try:
        while True:
            # 按钮1控制LED1
            if not button1.value():
                led1.on()
            else:
                led1.off()
            
            # 按钮2控制LED2（如果有）
            if has_led2:
                if not button2.value():
                    led2.on()
                else:
                    led2.off()
            
            # 两个按钮同时按下退出
            if not button1.value() and not button2.value():
                print("两个按钮同时按下，退出测试")
                break
            
            time.sleep(0.05)
    except KeyboardInterrupt:
        pass
    
    led1.off()
    if has_led2:
        led2.off()
    print("✓ 多按钮控制测试完成")

def main():
    """主函数"""
    print("YD-RP2040 按钮输入实验开始...")
    print("硬件连接要求：")
    print("1. 轻触按钮或跳线")
    print("2. LED（如果没有板载LED）")
    print("3. 可选：220Ω电阻")
    print()
    
    try:
        basic_button_read()
        button_counter()
        button_interrupt()
        multi_button_control()
    except Exception as e:
        print(f"实验出错：{e}")
    
    print("\n=== 实验2完成 ===")
    print("您已经掌握了GPIO输入控制和中断处理！")

if __name__ == "__main__":
    main()
